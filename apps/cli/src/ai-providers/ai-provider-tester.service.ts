import { Injectable, Logger } from '@nestjs/common';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatMistralAI } from '@langchain/mistralai';
import { ChatGroq } from '@langchain/groq';
import { HumanMessage } from '@langchain/core/messages';
import { AiProviderInfo } from './ai-provider.interface';

export interface AiProviderTestConfig {
    name: string;
    apiKey: string;
    model: string;
    temperature: number;
    maxTokens: number;
    baseUrl?: string;
}

export interface AiProviderTestResult {
    success: boolean;
    provider: string;
    model: string;
    responseTime: number;
    error?: string;
    response?: string;
}

@Injectable()
export class AiProviderTesterService {
    private readonly logger = new Logger(AiProviderTesterService.name);

    /**
     * Test an AI provider configuration
     */
    async testProvider(config: AiProviderTestConfig): Promise<AiProviderTestResult> {
        const startTime = Date.now();
        
        try {
            this.logger.log(`Testing ${config.name} provider with model ${config.model}...`);
            
            const llm = this.createProviderInstance(config);
            if (!llm) {
                return {
                    success: false,
                    provider: config.name,
                    model: config.model,
                    responseTime: Date.now() - startTime,
                    error: `Unsupported provider: ${config.name}`,
                };
            }

            // Test with a simple prompt
            const testMessage = new HumanMessage('Hello! Please respond with just "OK" to confirm you are working.');
            const response = await llm.invoke([testMessage]);
            
            const responseTime = Date.now() - startTime;
            const responseContent = response.content as string;

            this.logger.log(`${config.name} test completed in ${responseTime}ms`);

            return {
                success: true,
                provider: config.name,
                model: config.model,
                responseTime,
                response: responseContent.trim(),
            };

        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error(`${config.name} test failed: ${error.message}`);

            return {
                success: false,
                provider: config.name,
                model: config.model,
                responseTime,
                error: error.message,
            };
        }
    }

    /**
     * Create a provider instance for testing
     */
    private createProviderInstance(config: AiProviderTestConfig): any {
        const commonOptions = {
            apiKey: config.apiKey,
            temperature: config.temperature,
            maxTokens: config.maxTokens,
        };

        switch (config.name) {
            case 'openai':
                return new ChatOpenAI({
                    ...commonOptions,
                    model: config.model,
                    configuration: {
                        baseURL: config.baseUrl || 'https://api.openai.com',
                    },
                });

            case 'openrouter':
                return new ChatOpenAI({
                    ...commonOptions,
                    model: config.model,
                    configuration: {
                        baseURL: config.baseUrl || 'https://openrouter.ai/api/v1',
                    },
                });

            case 'ollama':
                return new ChatOpenAI({
                    ...commonOptions,
                    model: config.model,
                    configuration: {
                        baseURL: config.baseUrl || 'http://localhost:11434/v1',
                    },
                });

            case 'google':
                return new ChatOpenAI({
                    ...commonOptions,
                    model: config.model,
                    configuration: {
                        baseURL: 'https://generativelanguage.googleapis.com/v1beta/openai/',
                    },
                });

            case 'anthropic':
                return new ChatAnthropic({
                    ...commonOptions,
                    model: config.model,
                });

            case 'mistral':
                return new ChatMistralAI({
                    ...commonOptions,
                    model: config.model,
                });

            case 'groq':
                return new ChatGroq({
                    ...commonOptions,
                    model: config.model,
                });

            case 'deepseek':
                return new ChatOpenAI({
                    ...commonOptions,
                    model: config.model,
                    configuration: {
                        baseURL: config.baseUrl || 'https://api.deepseek.com',
                    },
                });

            default:
                return null;
        }
    }

    /**
     * Test multiple providers in sequence
     */
    async testMultipleProviders(configs: AiProviderTestConfig[]): Promise<AiProviderTestResult[]> {
        const results: AiProviderTestResult[] = [];

        for (const config of configs) {
            const result = await this.testProvider(config);
            results.push(result);
        }

        return results;
    }
}
