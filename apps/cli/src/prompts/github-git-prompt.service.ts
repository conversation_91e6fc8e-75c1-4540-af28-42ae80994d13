import { Injectable } from '@nestjs/common';
import { BasePromptService } from './base-prompt.service';

export interface GitHubGitConfig {
    githubApiKey: string;
    githubOwner: string;
    gitName: string;
    gitEmail: string;
}

@Injectable()
export class GitHubGitPromptService extends BasePromptService {
    async promptGitHubGitConfig(): Promise<GitHubGitConfig> {
        this.displaySectionHeader('GitHub & Git Configuration');
        this.displayInfo('Configure your GitHub API access and Git identity');

        const githubApiKey = await this.promptPassword(
            'Enter your GitHub API Key (Personal Access Token):'
        );

        const githubOwner = await this.promptRequiredText(
            'Enter your GitHub username/organization:'
        );

        const gitName = await this.promptRequiredText(
            'Enter your Git name (for commits):'
        );

        const gitEmail = await this.promptRequiredText(
            'Enter your Git email (for commits):'
        );

        this.displaySuccess('GitHub & Git configuration completed');

        return {
            githubApiKey,
            githubOwner,
            gitName,
            gitEmail,
        };
    }
}
