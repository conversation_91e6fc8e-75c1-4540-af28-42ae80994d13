import { Injectable } from '@nestjs/common';
import { BasePromptService } from './base-prompt.service';

export interface DeploymentConfig {
    provider: 'vercel' | 'ignore';
    vercelToken?: string;
}

@Injectable()
export class DeploymentPromptService extends BasePromptService {
    async promptDeploymentConfig(): Promise<DeploymentConfig> {
        this.displaySectionHeader('Deployment Provider Configuration');
        this.displayInfo('Configure your deployment provider (you can add more providers later)');

        const provider = await this.promptSelect(
            'Select a deployment provider:',
            [
                { name: 'Vercel', value: 'vercel' as const },
                { name: 'Skip deployment configuration', value: 'ignore' as const },
            ]
        );

        let vercelToken: string | undefined;

        if (provider === 'vercel') {
            this.displayInfo('You can get your Vercel token from: https://vercel.com/account/tokens');

            while (true) {
                try {
                    vercelToken = await this.promptPassword(
                        'Enter your Vercel token:'
                    );

                    const validation = this.validateApiKey(vercelToken, 'Vercel');
                    if (validation !== true) {
                        this.displayError(validation as string);
                        continue;
                    }

                    // Test the Vercel token
                    this.displayInfo('Testing Vercel token...');
                    const isValid = await this.testVercelToken(vercelToken);
                    if (!isValid) {
                        this.displayError('Vercel token is invalid or lacks required permissions');
                        this.displayInfo('Please check your token and try again');
                        continue;
                    }

                    this.displaySuccess('Vercel token validated successfully');
                    break;
                } catch (error) {
                    this.displayError('Failed to validate Vercel token. Please try again.');
                }
            }
        }

        if (provider !== 'ignore') {
            this.displaySuccess(`${provider} deployment configuration completed`);
        } else {
            this.displayInfo('Deployment configuration skipped');
        }

        return {
            provider,
            vercelToken,
        };
    }

    private async testVercelToken(token: string): Promise<boolean> {
        try {
            const response = await fetch('https://api.vercel.com/v2/user', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });
            return response.ok;
        } catch {
            return false;
        }
    }
}
