{"name": "ever-works-cli", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "pnpm build:deps && nest start -b swc", "start:dev": "pnpm build:deps && nest start -b swc --watch", "start:debug": "pnpm build:deps && nest start -b swc --debug --watch", "start:prod": "node dist/main", "build": "pnpm build:deps && nest build -b swc", "build:deps": "pnpm --filter '@packages/*' build"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "nest-commander": "^3.17.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "@packages/agent": "workspace:*"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@swc/cli": "^0.6.0", "@swc/core": "^1.12.9", "@types/node": "^22.16.0", "globals": "^16.3.0", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}